/* eslint-disable @typescript-eslint/no-explicit-any */

import Cookies from 'universal-cookie'

import api from './api'
import { clientGraphql } from './graphql'

export function setToken(ctx: any) {
  const cookies = new Cookies(ctx.req.cookies, { path: '/' })
  const token = cookies.get('b2bAuthToken')

  api.defaults.headers.common['Authorization'] = `Bearer ${token}`
  clientGraphql.setHeader('Authorization', `Bearer ${token}`)
}
