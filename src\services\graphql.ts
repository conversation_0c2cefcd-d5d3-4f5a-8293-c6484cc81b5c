import { GraphQLClient } from 'graphql-request'
import Cookies from 'universal-cookie'

const endpoint = `${process.env.NEXT_PUBLIC_API_URL}/graphql`
const clientGraphql = new GraphQLClient(endpoint)

const cookies = new Cookies(null, { path: '/' })
const b2bAuthToken = cookies.get('b2bAuthToken')

if (b2bAuthToken) {
  clientGraphql.setHeader('Authorization', `Bearer ${b2bAuthToken}`)
}

export { clientGraphql }
