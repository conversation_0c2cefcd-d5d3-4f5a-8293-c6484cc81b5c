import {
  Button,
  DropdownItem,
  DropdownItemContent,
} from '@ads/components-react'
import { Trash2 } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

import { Separator } from '../ui/separator'

type DeleteStudyPlanAlertProps = {
  studyPlanMutation: () => void
}

export function DeleteStudyPlanAlert({
  studyPlanMutation,
}: DeleteStudyPlanAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownItem status="destructive" onSelect={(e) => e.preventDefault()}>
          <DropdownItemContent leadingIcon={Trash2}>
            Excluir
          </DropdownItemContent>
        </DropdownItem>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Tem certeza que deseja excluir trilha?
          </AlertDialogTitle>
          <AlertDialogDescription>
            A exclusão da trilha de aprendizado é irreversível.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Separator />
        <AlertDialogFooter>
          <AlertDialogAction asChild>
            <Button onClick={studyPlanMutation} hierarchy="tertiary">
              Excluir
            </Button>
          </AlertDialogAction>
          <AlertDialogCancel asChild>
            <Button>Voltar</Button>
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
