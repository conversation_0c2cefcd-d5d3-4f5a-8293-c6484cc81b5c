'use client'

import { IconButton, LinkButton } from '@ads/components-react'
import Image from 'next/image'
import Link from 'next/link'
import { RxHamburgerMenu } from 'react-icons/rx'
import { TfiHeadphoneAlt } from 'react-icons/tfi'

import { Separator } from '@/components/ui/separator'
import { useSidebar } from '@/components/ui/sidebar'

import { AvatarDropdown } from './avatar'

export function Header() {
  const { isMobile, openMobile, setOpenMobile } = useSidebar()

  return (
    <div className="flex h-16 w-full items-center justify-between bg-ctx-layout-body px-4 md:px-8">
      {isMobile && (
        <IconButton
          icon={RxHamburgerMenu}
          hierarchy="secondary"
          ariaLabel="Expandir menu lateral"
          onClick={() => setOpenMobile(!openMobile)}
        />
      )}
      <div>
        <Link href="/chat">
          <Image
            src={'/assets/solution.webp'}
            alt="Plataforma Solution"
            width={28}
            height={28}
          />
        </Link>
      </div>
      <div className="flex items-center gap-4">
        <Link
          href="https://api.whatsapp.com/send?phone=551926603120"
          target="_blank"
        >
          <LinkButton
            hierarchy="secondary"
            leadingIcon={TfiHeadphoneAlt}
            className="ts-paragraph-xxs"
          >
            Precisa de ajuda?
          </LinkButton>
        </Link>
        <Separator orientation="vertical" className="h-10" />
        <AvatarDropdown />
      </div>
    </div>
  )
}
