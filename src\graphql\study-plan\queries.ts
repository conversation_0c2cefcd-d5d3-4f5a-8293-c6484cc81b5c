import { gql } from 'graphql-request'

export const GET_STUDY_PLANS = gql`
  query getStudyPlans(
    $user_id: Float
    $name: String
    $company_id: Float!
    $limit: Float
    $page: Float
    $onlyPDI: Boolean
    $status: Boolean
    $end_date: DateTime
  ) {
    studyPlans(
      name: $name
      company_id: $company_id
      limit: $limit
      page: $page
      onlyPDI: $onlyPDI
      user_id: $user_id
      status: $status
      end_date: $end_date
    ) {
      data {
        courses_pivot {
          course_id
          course {
            id
            title
          }
        }
        ai_generated
        end_date
        status
        id
        name
        coursesCount
        squadsCount
        usersCount
      }
      total
      perPage
    }
  }
`
