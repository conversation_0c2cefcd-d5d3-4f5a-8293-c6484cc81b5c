'use client'

import {
  <PERSON><PERSON>,
  But<PERSON>,
  Dropdown,
  DropdownItem,
  DropdownItemContent,
  Switch,
} from '@ads/components-react'
import { <PERSON>, User } from 'lucide-react'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import { IoExitOutline } from 'react-icons/io5'

import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { useAuth } from '@/contexts/AuthContext'

export function AvatarDropdown() {
  const { user, isLoading, signOut } = useAuth()

  const { theme, setTheme } = useTheme()

  const isDarkTheme = theme === 'dark'

  return (
    <div className="flex items-center">
      <Dropdown
        trigger={
          <Button hierarchy="tertiary" className="!p-0">
            {isLoading ? (
              <Skeleton className="size-8 animate-pulse rounded-full bg-base-gray-200" />
            ) : (
              <Avatar src={user?.avatar?.url} fallback={user?.name} size="md" />
            )}
          </Button>
        }
        key="desktop"
        align="end"
        className="gap-4 rounded-3xl p-4"
      >
        <div className="flex items-center gap-4">
          <Avatar src={user?.avatar?.url} fallback={user?.name} size="md" />
          <span className="line-clamp-1 w-[25ch]">{user?.name}</span>
        </div>
        <div className="flex w-full flex-col text-ctx-content-title ts-paragraph-xxs">
          <Link href="/perfil">
            <DropdownItem data-orientation="horizontal">
              <DropdownItemContent leadingIcon={User}>
                Perfil
              </DropdownItemContent>
            </DropdownItem>
          </Link>

          <Separator />

          <DropdownItem
            onClick={(event) => {
              event.preventDefault()
              setTheme(isDarkTheme ? 'light' : 'dark')
            }}
          >
            <DropdownItemContent leadingIcon={Moon}>
              Modo Escuro
            </DropdownItemContent>
            <Switch checked={isDarkTheme} />
          </DropdownItem>

          <Separator />

          <DropdownItem status="destructive" onClick={() => signOut()}>
            <DropdownItemContent leadingIcon={IoExitOutline}>
              Sair
            </DropdownItemContent>
          </DropdownItem>
        </div>
      </Dropdown>
    </div>
  )
}
