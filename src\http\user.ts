import { GET_ENROLLMENTS_B2B_BY_COMPANY_ID } from '@/graphql/enrollments/queries'
import { GetEnrollmentsByCompanyIdQuery } from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export const getEnrollmentsByCompanyIdUsers = (criteria: string) =>
  clientGraphql.request<GetEnrollmentsByCompanyIdQuery>(
    GET_ENROLLMENTS_B2B_BY_COMPANY_ID,
    {
      criteria,
    }
  )
