interface ResponseAIProps {
  content?: string
}

export function ResponseAI({ content }: ResponseAIProps) {
  return (
    <div className="flex w-full flex-col space-y-2">
      <h3 className="text-ctx-content-title ts-subtitle-xs">Solu IA:</h3>
      <div className="prose-span:text-ctx-content-base prose prose-p:text-ctx-content-base prose-p:ts-paragraph-xs prose-strong:text-ctx-content-base prose-li:text-ctx-content-base">
        <div dangerouslySetInnerHTML={{ __html: content ?? '' }} />
      </div>
    </div>
  )
}
