'use client'

import { useParams } from 'next/navigation'
import { useQuery } from 'react-query'

import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { getChatMessageAI } from '@/http/chat-ai/get-chat-ai-message'

import { ChatAI } from './chat-ai'

export default function ChatId() {
  const params = useParams()
  const chatId = params.id

  const { data, isLoading } = useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => getChatMessageAI(chatId as string),
  })

  return (
    <div className="flex h-full w-full flex-col md:px-4 md:pt-6">
      {isLoading ? (
        <Skeleton className="h-8 w-80" />
      ) : (
        <h1 className="text-ctx-content-title ts-heading-sm md:ts-heading-lg">
          {data?.[0]?.ai_response.title}
        </h1>
      )}

      <Separator className="my-2 md:my-4" />

      <ChatAI />
    </div>
  )
}
