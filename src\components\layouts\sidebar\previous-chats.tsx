'use client'

import { useQuery } from 'react-query'

import { Spinner } from '@/components/loaders/spinner'
import { SidebarGroup, SidebarMenu } from '@/components/ui/sidebar'
import { getChatHistory } from '@/http/chats'
import { convertDate } from '@/utils/convert-date'

import { HistoryItem } from './history-item'

export function PreviousChats() {
  const { data, isLoading } = useQuery({
    queryKey: ['history-chats'],
    queryFn: () => getChatHistory(),
  })

  if (isLoading) return <Spinner />

  return (
    <SidebarGroup className="overflow-y-auto">
      <SidebarMenu className="gap-6 px-6 py-4">
        {data?.today && (
          <div className="flex flex-col gap-2">
            <span className="text-ctx-content-base ts-heading-xxs">Hoje</span>
            {data.today.map((item) => (
              <HistoryItem key={item.id} item={item} />
            ))}
          </div>
        )}
        {data && data?.yesterday.length > 0 && (
          <div className="flex flex-col gap-2">
            <span className="text-ctx-content-base ts-heading-xxs">Ontem</span>
            {data.yesterday.map((item) => (
              <HistoryItem key={item.id} item={item} />
            ))}
          </div>
        )}
        {data && data?.lastweek.length > 0 && (
          <div className="flex flex-col gap-2">
            <span className="text-ctx-content-base ts-heading-xxs">
              Semana passada
            </span>
            {data.lastweek.map((item) => (
              <HistoryItem key={item.id} item={item} />
            ))}
          </div>
        )}
        {data &&
          Object.entries(data.pastmonths).length > 0 &&
          Object.entries(data.pastmonths).map((item) => (
            <div className="flex flex-col gap-2" key={item[0]}>
              <span className="capitalize text-ctx-content-base ts-heading-xxs">
                {convertDate(new Date(item[0]))}
              </span>
              {item[1].map((chat) => (
                <HistoryItem key={chat.id} item={chat} />
              ))}
            </div>
          ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}
