export interface Course {
  id: number
  title: string
  justification: string
  description: string
  image_url: string
}

export interface Suggestion {
  title: string
  description: string
  count: number
  content?: string
  courses: Course[]
}

export interface ChatAI {
  chat_id: string
  user_input: string
  suggestion: Suggestion
}

type AIResponse = Suggestion

export interface ResponseAI {
  id: string
  chat_id: string
  user_input: string
  ai_response: AIResponse
  ai_chat_id: string
}

export interface MessageChatAI {
  context: string
}
