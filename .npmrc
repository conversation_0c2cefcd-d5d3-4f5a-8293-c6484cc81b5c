registry=https://registry.npmjs.org/
@ads:registry=https://pkgs.dev.azure.com/pecegedev/_packaging/ads/npm/registry/

always-auth=true

; begin auth token
//pkgs.dev.azure.com/pecegedev/_packaging/ads/npm/registry/:username=pecegedev
//pkgs.dev.azure.com/pecegedev/_packaging/ads/npm/registry/:_password=Mkd3V0VGdEpHVDQ1ZGEzTXI3U21ReDJUSVlpSU5vQWptYkhGTThzTk1UUDFreXU2eHRXVkpRUUo5OUJFQUNBQUFBQXR3TlBOQUFBU0FaRE8yb1lw
//pkgs.dev.azure.com/pecegedev/_packaging/ads/npm/registry/:email=<EMAIL>
//pkgs.dev.azure.com/pecegedev/_packaging/ads/npm/:username=pecegedev
//pkgs.dev.azure.com/pecegedev/_packaging/ads/npm/:_password=Mkd3V0VGdEpHVDQ1ZGEzTXI3U21ReDJUSVlpSU5vQWptYkhGTThzTk1UUDFreXU2eHRXVkpRUUo5OUJFQUNBQUFBQXR3TlBOQUFBU0FaRE8yb1lw
//pkgs.dev.azure.com/pecegedev/_packaging/ads/npm/:email=<EMAIL>
; end auth token