'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  StepperItem,
} from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import { Step1 } from './step1'
import { CombinedCheckoutSchema, CombinedCheckoutType } from './validations'

export default function CriarTrilha() {
  const [activeStep, setActiveStep] = useState(0)

  const methods = useForm<CombinedCheckoutType>({
    resolver: zodResolver(CombinedCheckoutSchema),
  })

  const handleSubmit = (data: CombinedCheckoutType) => {
    console.log(data)
  }

  return (
    <FormProvider {...methods}>
      <form
        className="m-auto flex w-full flex-col gap-8 px-6 py-8"
        onSubmit={methods.handleSubmit(handleSubmit)}
      >
        <div>
          <h1 className="text-ctx-content-title ts-heading-md">
            Tri<PERSON><PERSON> de aprendizagem
          </h1>
          <span className="text-ctx-content-base ts-paragraph-xs">
            <PERSON><PERSON><PERSON><PERSON>, acompanhe e analise o progresso de desenvolvimento dos
            colaboradores em tempo real.
          </span>
        </div>
        <div className="flex w-full justify-center">
          <Stepper activeStep={activeStep}>
            <StepperItem title="Option 1" />
            <StepperItem title="Option 2" />
            <StepperItem title="Option 3" />
          </Stepper>
        </div>
        <div>
          <StepperContent activeStep={activeStep} index={0}>
            <Step1 />
          </StepperContent>
          <StepperContent activeStep={activeStep} index={1}>
            <p>Content 2</p>
          </StepperContent>
          <StepperContent activeStep={activeStep} index={2}>
            <p>Content 3</p>
          </StepperContent>
        </div>

        <div className="flex w-full justify-end">
          <Button
            type="button"
            onClick={() => setActiveStep(activeStep - 1)}
            className="mr-2"
          >
            Voltar
          </Button>
          <Button type="submit">Próximo</Button>
        </div>
      </form>
    </FormProvider>
  )
}
