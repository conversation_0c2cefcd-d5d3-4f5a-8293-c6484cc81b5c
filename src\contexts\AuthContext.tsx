import { useAlert } from '@ads/components-react'
import axios from 'axios'
import { useRouter } from 'next/navigation'
import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'
import { useMutation } from 'react-query'
import Cookies from 'universal-cookie'

import { Role } from '@/enum/role'
import { getUserById } from '@/http/get-user-by-id'
import { getActivesLicenses } from '@/http/license'
import { getUserProfile } from '@/http/profile'
import { getSessioAdmin } from '@/http/session'
import { getEnrollmentsByCompanyIdUsers } from '@/http/user'
import { Avatar } from '@/model/avatar'
import { cookieSettings } from '@/utils/cookie-settings'

import api from '../services/api'
import { clientGraphql } from '../services/graphql'

type AuthContextType = {
  signIn: ({ email, password }: SignInProps) => Promise<void>
  signOut: () => void
  setToken: (user: User, token: string) => void
  updateUser: (userId: number) => Promise<void>
  user: User | null
  isLoading?: boolean
}

type AuthContextProps = {
  children: ReactNode
}

type SignInProps = {
  email: string
  password: string
}

type User = {
  name: string
  phone_number: string
  roles: {
    id: number
    name: string
    slug: string
  }[]
  id: number
  avatar: Avatar | null
  metadata: {
    company_id: number | null
  }
}

export const AuthContext = createContext({} as AuthContextType)

export function AuthProvider({ children }: AuthContextProps) {
  const { alert } = useAlert()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const initialized = useRef(false)
  const cookies = new Cookies(null, { path: '/' })

  const userHavePermissionB2B = useCallback((user: User): boolean => {
    return user.roles.some((role) => role.slug === Role.B2B_MANAGER)
  }, [])

  const userHavePermissionAdmin = useCallback((user: User): boolean => {
    return user.roles.some((role) => role.slug === Role.ADMIN)
  }, [])

  const setB2BAuthTokens = useCallback((user: User, token: string) => {
    cookies.set('b2bAuthToken', token, cookieSettings())
    cookies.set('b2bUserId', JSON.stringify(user.id), cookieSettings())
  }, [])

  const verifyPermissionsByLicense = useCallback(async () => {
    try {
      const licenses = await getActivesLicenses()
      const typesLicenses = licenses.userEnrollments.map(
        (license) => license.type
      )

      const permissions = typesLicenses.filter(
        (type, index, arr) => arr.indexOf(type) === index
      )

      cookies.set(
        'lmsPermissions',
        JSON.stringify(permissions),
        cookieSettings({
          withDomain: true,
        })
      )
    } catch (err) {
      console.error(err)
    }
  }, [])

  const { mutate: profileMutation } = useMutation(() => getUserProfile(), {
    onSuccess: (response) => {
      cookies.set(
        'userPlan',
        String(response.profile.payment_plan?.slug),
        cookieSettings({
          withDomain: true,
        })
      )
    },
    onError: (error) => {
      console.error(error)
    },
  })

  const setToken = useCallback(
    (user: User, token: string): void => {
      setUser(user)

      setB2BAuthTokens(user, token)

      cookies.set(
        'b2bCompanyId',
        JSON.stringify(user.metadata.company_id),
        cookieSettings()
      )

      cookies.set('lmsAuthToken', token, cookieSettings({ withDomain: true }))

      cookies.set(
        'lmsUserId',
        JSON.stringify(user.id),
        cookieSettings({ withDomain: true })
      )

      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      clientGraphql.setHeader('Authorization', `Bearer ${token}`)

      profileMutation()
      verifyPermissionsByLicense()
    },
    [setB2BAuthTokens, profileMutation, verifyPermissionsByLicense]
  )

  const sessionAdmin = useMutation(
    ({ email, password }: SignInProps) =>
      getSessioAdmin({
        email,
        password,
      }),
    {
      onSuccess: ({ user, token }) => {
        const isManagerB2B = userHavePermissionB2B(user)

        if (!isManagerB2B) {
          const isAdmin = userHavePermissionAdmin(user)

          if (!isAdmin) {
            alert({
              alertType: 'danger',
              title: 'Sem permissão',
              description: 'Você não tem permissão de acesso!',
            })
            return
          }

          alert({
            alertType: 'danger',
            title: 'Sem permissão',
            description: 'Você não tem permissão de acesso!',
          })
          // TODO: Ainda será implementado quando a página de selecionar empresa existir
          // setUser(user)
          // setB2BAuthTokens(user, token)
          return
        }

        if (user.metadata.company_id) {
          mutate(user?.metadata?.company_id)
        }

        if (user.metadata.company_id === null) {
          alert({
            alertType: 'danger',
            title: 'Acesso negado',
            description:
              'Entre em contato com o suporte ou sua empresa para obter mais detalhes.',
          })

          return
        }

        setUser(user)
        setToken(user, token)
        router.push('/chat')
      },
      onError: (err) => {
        if (
          axios.isAxiosError(err) &&
          err.response?.data.error === 'SES-0001'
        ) {
          alert({
            alertType: 'danger',
            title: 'Acesso negado',
            description: 'E-mail ou senha inválidos',
          })

          return
        }
        alert({
          alertType: 'danger',
          title: 'Acesso negado',
          description: 'Aconteceu algum problema. Tente novamente!',
        })
      },
    }
  )

  const updateUser = useCallback(async (userId: number) => {
    try {
      const response = await getUserById(userId)
      const userData = response?.data || response

      if (userData) {
        setUser(userData)
      }
    } catch {
      alert({
        alertType: 'danger',
        description: 'Erro ao atualizar o usuário.',
      })
    }
  }, [])

  const { mutate } = useMutation(
    (company_id: number) => getEnrollmentsByCompanyIdUsers(String(company_id)),
    {
      onSuccess: (response) => {
        if (response?.company?.enrollments) {
          cookies.set(
            'b2bEnrollmentId',
            response?.company?.enrollments[0]?.enrollment_id,
            cookieSettings()
          )
        }
      },
      onError: (error) => {
        console.error(error)
      },
    }
  )

  const clearAuthCookies = () => {
    cookies.remove('b2bCompanyId')
    cookies.remove('b2bUserId')
    cookies.remove('b2bAuthToken')
    cookies.remove('b2bEnrollmentId')
  }

  const restoreUserSession = useCallback(async () => {
    if (initialized.current) return

    const token = cookies.get('b2bAuthToken')
    const userId = cookies.get('b2bUserId')

    if (!token || !userId) {
      setIsLoading(false)
      initialized.current = true
      return
    }

    try {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      clientGraphql.setHeader('Authorization', `Bearer ${token}`)

      const parsedUserId =
        typeof userId === 'string' ? JSON.parse(userId) : userId
      const response = await getUserById(parsedUserId)

      const userData = response?.data || response

      if (userData) {
        setUser(userData)
      }
    } catch {
      clearAuthCookies()
    } finally {
      setIsLoading(false)
      if (!initialized.current) initialized.current = true
    }
  }, [cookies])

  useEffect(() => {
    restoreUserSession()
  }, [restoreUserSession])

  const signIn = useCallback(
    async ({ email, password }: SignInProps) => {
      sessionAdmin.mutate({
        email,
        password,
      })
    },
    [sessionAdmin]
  )

  const signOut = useCallback(() => {
    clearAuthCookies()

    router.push('/login')
  }, [router])

  return (
    <AuthContext.Provider
      value={{ signIn, user, isLoading, updateUser, setToken, signOut }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
}
