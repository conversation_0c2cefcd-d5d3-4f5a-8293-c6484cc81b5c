import type { CodegenConfig } from '@graphql-codegen/cli'

const config: CodegenConfig = {
  overwrite: true,
  schema: 'https://solutiona-api-dev-container.azurewebsites.net/graphql',
  documents: ['src/graphql/**/{queries,mutations}.ts'],
  config: {
    namingConvention: {
      typeNames: 'change-case-all#pascalCase',
      enumValues: 'change-case-all#upperCase',
      transformUnderscore: true,
    },
    ignoreNoDocuments: true,
  },
  generates: {
    'src/graphql/generated/': {
      preset: 'client',
      config: {
        skipTypename: true,
        avoidOptionals: true,
        namingConvention: {
          typeNames: 'change-case-all#pascalCase',
          enumValues: 'change-case-all#upperCase',
          transformUnderscore: true,
        },
      },
      plugins: [],
    },
  },
}

export default config
