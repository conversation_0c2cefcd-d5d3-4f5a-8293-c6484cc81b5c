import { Skeleton } from '@/components/ui/skeleton'

export function ChatSkeleton() {
  return (
    <div className="h-[calc(100vh-320px)] flex-1 md:h-[calc(100vh-388px)]">
      <div className="mx-auto flex w-full max-w-[58rem] flex-col space-y-4 p-4">
        <div className="mb-8 flex w-full justify-end">
          <Skeleton className="h-10 w-full max-w-[500px] animate-pulse rounded-lg" />
        </div>
        <div className="flex w-full max-w-[58rem] flex-col space-y-4 pb-8">
          <Skeleton className="h-5 max-w-[100px] animate-pulse rounded-lg" />
          <Skeleton className="h-5 max-w-[500px] animate-pulse rounded-lg" />
          <Skeleton className="h-5 max-w-[300px] animate-pulse rounded-lg" />
        </div>
        <div>
          <Skeleton className="flex animate-pulse flex-col items-center gap-9 rounded-lg p-3 md:flex-row">
            <div className="h-[200px] w-[172px] rounded bg-gray-300" />
            <div className="flex w-full flex-col gap-4 rounded">
              <div className="mb-4 h-5 max-w-96 rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
            </div>
          </Skeleton>
        </div>
        <div>
          <Skeleton className="flex animate-pulse flex-col items-center gap-9 rounded-lg p-3 md:flex-row">
            <div className="h-[200px] w-[172px] rounded bg-gray-300" />
            <div className="flex w-full flex-col gap-4 rounded">
              <div className="mb-4 h-5 max-w-96 rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
            </div>
          </Skeleton>
        </div>
      </div>
      <div className="mx-auto flex w-full max-w-[58rem] flex-col space-y-4 p-4">
        <div className="mb-8 flex w-full justify-end">
          <Skeleton className="h-10 w-full max-w-[500px] animate-pulse rounded-lg" />
        </div>
        <div className="flex w-full max-w-[58rem] flex-col space-y-4 pb-8">
          <Skeleton className="h-5 max-w-[100px] animate-pulse rounded-lg" />
          <Skeleton className="h-5 max-w-[500px] animate-pulse rounded-lg" />
          <Skeleton className="h-5 max-w-[300px] animate-pulse rounded-lg" />
        </div>
        <div>
          <Skeleton className="flex animate-pulse flex-col items-center gap-9 rounded-lg p-3 md:flex-row">
            <div className="h-[200px] w-[172px] rounded bg-gray-300" />
            <div className="flex w-full flex-col gap-4 rounded">
              <div className="mb-4 h-5 max-w-96 rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
            </div>
          </Skeleton>
        </div>
        <div>
          <Skeleton className="flex animate-pulse flex-col items-center gap-9 rounded-lg p-3 md:flex-row">
            <div className="h-[200px] w-[172px] rounded bg-gray-300" />
            <div className="flex w-full flex-col gap-4 rounded">
              <div className="mb-4 h-5 max-w-96 rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
              <div className="h-5 max-w-[550px] rounded bg-gray-300" />
            </div>
          </Skeleton>
        </div>
      </div>
    </div>
  )
}
