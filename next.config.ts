import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  /* config options here */

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'plataformasolutiondev.blob.core.windows.net',
      },
      {
        protocol: 'https',
        hostname: 'plataformasolutionprd.blob.core.windows.net',
      },
      {
        protocol: 'https',
        hostname: 'hma-aluno.plataformasolution.com.br',
      },
    ],
  },
}

export default nextConfig
