import Link from 'next/link'
import { usePathname } from 'next/navigation'

import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar'

type HistoryItemProps = {
  item: {
    id: number
    title: string
    created_at: string
  }
}

export function HistoryItem({ item }: HistoryItemProps) {
  const pathname = usePathname()

  return (
    <SidebarMenuItem key={item.id} className="px-1">
      <SidebarMenuButton
        asChild
        isActive={pathname === `/chat/${item.id}`}
        tooltip={item.title}
        className="data-[active=true]:group-data-[collapsible=icon]:!h-10 data-[active=true]:group-data-[collapsible=icon]:!w-10 data-[active=true]:group-data-[collapsible=icon]:!p-2.5"
      >
        <Link href={`/chat/${item.id}`}>
          <span className="text-ctx-content-base ts-paragraph-xxs">
            {item.title}
          </span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}
