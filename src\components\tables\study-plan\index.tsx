'use client'

import { Tag } from '@ads/components-react'
import { HiOutlineSparkles } from 'react-icons/hi2'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetStudyPlansQuery } from '@/graphql/generated/graphql'
import { StudyPlanData } from '@/model/study-plan'
import { convertDateToCalendar } from '@/utils/convert-date'

import { DropdownOptions } from './dropdown-options'

type StudyPlanTableProps = {
  studyPlans: GetStudyPlansQuery['studyPlans']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function StudyPlanTable({
  studyPlans,
  isLoading,
  currentPage,
  setCurrentPage,
  isFetchingNewPage = false,
}: StudyPlanTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Number(studyPlans.total / studyPlans.perPage),
    itemsPerPage: studyPlans.perPage,
    totalItems: studyPlans.total,
  }

  const handleTagLabel = (studyPlan: StudyPlanData) => {
    if (studyPlan.status) {
      return 'Ativo'
    }

    if (studyPlan.end_date && new Date(studyPlan.end_date) > new Date()) {
      return 'Vencido'
    }

    return 'Inativo'
  }

  const handleHandleTag = (studyPlan: StudyPlanData) => {
    if (studyPlan.status) {
      return 'highlight'
    }

    if (studyPlan.end_date && new Date(studyPlan.end_date) > new Date()) {
      return 'positive'
    }

    return 'helper'
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const columns: TableColumn<StudyPlanData>[] = [
    {
      key: 'name',
      header: 'Nome',
      accessor: (studyPlan) => (
        <span className="flex items-center gap-4">
          {studyPlan.name}
          {studyPlan.ai_generated && (
            <HiOutlineSparkles className="text-base-accent-600" />
          )}
        </span>
      ),
    },
    {
      key: 'coursesCount',
      header: 'Cursos',
      accessor: 'coursesCount',
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'squadsCount',
      header: 'Equipes',
      accessor: 'squadsCount',
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'usersCount',
      header: 'Colaboradores',
      accessor: 'usersCount',
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'usersCompletedCount',
      header: 'Concluíram',
      accessor: 'coursesCount',
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'end_date',
      header: 'Vencimento',
      accessor: ({ end_date }) =>
        end_date ? convertDateToCalendar(new Date(end_date as Date)) : '-',
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'status',
      header: 'Status',
      accessor: (studyPlan) => (
        <Tag
          label={handleTagLabel(studyPlan)}
          status={handleHandleTag(studyPlan)}
        />
      ),
    },
    {
      key: 'id',
      header: '',
      accessor: (studyPlan) => <DropdownOptions studyPlan={studyPlan} />,
    },
  ]

  return (
    <DataTable
      data={studyPlans.data as StudyPlanData[]}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhuma trilha encontrada."
    />
  )
}
