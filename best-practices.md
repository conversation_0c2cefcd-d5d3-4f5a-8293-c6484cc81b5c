-  "Hooks" sempre no topo dos componentes antes do "states".
- Usar "useEffect" sempre em "último caso" ou apenas quando realmente houver efetiva necessidade.
- No projeto não há problema utilizar "function()" e "arrow functions", por<PERSON><PERSON>, se possível, padronize o componente que vocês está utilizando.
- Dê preferências para utilizar o primeiro o ADS -> SHADCN -> Outras Libs
- NUNCA 2 componentes no mesmo arquivo.
- Ao criar novos componentes, caso tenha dúvida da estrutura de pastas, converse com o time para avaliar a melhor maneira ou padrão.