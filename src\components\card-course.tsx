'use client'

import Image from 'next/image'

import { cn } from '@/lib/utils'

import { CustomCheckbox } from './custom-checkbox'

interface CardCourseProps {
  id: string
  index: number
  imageUrl: string
  imageAlt: string
  title: string
  justification: string
  checked: boolean
  onChange: (id: string, checked: boolean) => void
}

export function CardCourse({
  id,
  index,
  imageUrl,
  imageAlt,
  title,
  justification,
  checked,
  onChange,
}: CardCourseProps) {
  function handleCheckboxChange() {
    onChange(id, !checked)
  }
  return (
    <div
      role="button"
      tabIndex={0}
      onClick={handleCheckboxChange}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          handleCheckboxChange()
        }
      }}
      className={cn(
        'w-full max-w-[908px] cursor-pointer rounded-xl border border-solid bg-ctx-interactive-secondary p-3 pl-8 transition-all duration-200 hover:bg-ctx-interactive-secondaryHover',
        checked
          ? 'border-ctx-highlight-focus'
          : 'border-ctx-interactive-secondary'
      )}
    >
      <div className="flex justify-between">
        <div className="flex flex-col md:flex-row md:items-center">
          <div className="mb-8 flex h-[250px] w-full max-w-[170px] items-center justify-center rounded-lg border border-solid border-ctx-interactive-secondary bg-ctx-content-placeholderAlternative md:mb-0">
            <div className="relative h-[226px] w-[146px]">
              <Image
                src={imageUrl}
                alt={imageAlt}
                fill
                className="rounded-md object-cover"
              />
            </div>
          </div>
          <div className="md:px-8">
            <p className="mb-8 text-ctx-content-title ts-paragraph-xs">
              Curso {index}: {title}
            </p>
            <span className="text-ctx-content-base ts-paragraph-xs">
              Justificativa: {justification}
            </span>
          </div>
        </div>
        <div>
          <CustomCheckbox
            checked={checked}
            onChange={() => onChange(id, !checked)}
          />
        </div>
      </div>
    </div>
  )
}
