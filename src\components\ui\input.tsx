import { DynamicIcon, IconName } from 'lucide-react/dynamic'
import * as React from 'react'

import { cn } from '@/lib/utils'

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  errorMessage?: string
  endIcon?: IconName
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, errorMessage, label, endIcon, ...props }, ref) => {
    return (
      <div className="flex w-full flex-col gap-1.5">
        {label && (
          <label className="font-normal" htmlFor={props.id}>
            {label}
          </label>
        )}
        <div className="relative">
          <input
            type={type}
            data-slot="input"
            className={cn(className)}
            ref={ref}
            aria-invalid={!!errorMessage}
            {...props}
          />
          {endIcon && (
            <DynamicIcon
              name={endIcon}
              className="absolute right-4 top-1/2 h-4 w-4 -translate-y-1/2 transform opacity-50"
            />
          )}
        </div>
        {errorMessage && (
          <p className="text-xs font-normal text-red-400">{errorMessage}</p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export { Input }
