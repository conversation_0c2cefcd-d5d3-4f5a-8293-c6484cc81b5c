type PageLayoutProps = {
  title: string
  description?: string
  children: React.ReactNode
}

export function PageLayout({ children, title, description }: PageLayoutProps) {
  return (
    <div className="px-4 py-6 md:px-8">
      <div className="mb-8">
        <h1 className="text-ctx-content-title ts-heading-md">{title}</h1>
        {description && (
          <span className="text-ctx-content-base ts-paragraph-xs">
            {description}
          </span>
        )}
      </div>
      {children}
    </div>
  )
}
