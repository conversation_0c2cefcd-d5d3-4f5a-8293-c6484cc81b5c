import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

// Rotas que não precisam de autenticação
const publicRoutes = ['/', '/api']

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  const token = request.cookies.get('b2bAuthToken')

  if (pathname === '/') {
    if (token) {
      // Usuário já está autenticado, redireciona para dashboard
      return NextResponse.redirect(new URL('/chat', request.url))
    }
    return NextResponse.next()
  }

  // Verificar se é a rota raiz
  if (pathname === '/') {
    if (token) {
      // Usuário está logado, redireciona para dashboard
      return NextResponse.redirect(new URL('/chat', request.url))
    } else {
      return NextResponse.redirect(new URL('/', request.url))
    }
  }

  // Verificar se a rota é pública
  if (
    publicRoutes.some(
      (route) => pathname === route || pathname.startsWith(`${route}/`)
    )
  ) {
    return NextResponse.next()
  }

  if (!token) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/chat',
    '/api',
    '/((?!_next/static|_next/image|favicon.ico|assets/).*)',
  ],
}
