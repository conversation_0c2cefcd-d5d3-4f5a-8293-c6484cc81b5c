import {
  Dropdown,
  DropdownItem,
  DropdownItem<PERSON>ontent,
  Icon<PERSON>utton,
  useAlert,
} from '@ads/components-react'
import { Edit, EllipsisVertical, Files, Info, Power } from 'lucide-react'
import Link from 'next/link'
import { useMutation, useQueryClient } from 'react-query'

import { DeleteStudyPlanAlert } from '@/components/alerts-dialogs/delete-studyplan'
import { Separator } from '@/components/ui/separator'
import { deleteStudyPlan } from '@/http/study-plan'
import { StudyPlanData } from '@/model/study-plan'

type DropdownOptionsProps = {
  studyPlan: StudyPlanData
}

export function DropdownOptions({ studyPlan }: DropdownOptionsProps) {
  const queryClient = useQueryClient()
  const { alert } = useAlert()

  const { mutate: deleteMutation } = useMutation({
    mutationFn: () => deleteStudyPlan(studyPlan.id),
    onSuccess: () => {
      queryClient.invalidateQueries(['getStudyPlansGql'])
      alert({
        title: 'Trilha de aprendizado excluída com sucesso!',
        description: `A trilha ${studyPlan.name} foi excluída com sucesso.`,
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao excluir trilha de aprendizado',
        description:
          'Ocorreu um erro ao excluir a trilha de aprendizado. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  const handleDelete = () => {
    deleteMutation()
  }

  return (
    <Dropdown
      className="w-[216px]"
      align="end"
      trigger={
        <IconButton
          ariaLabel="Editar"
          icon={EllipsisVertical}
          hierarchy="tertiary"
          className="!p-0"
        />
      }
    >
      <DropdownItem>
        <DropdownItemContent leadingIcon={Info}>
          Detalhes da Trilha
        </DropdownItemContent>
      </DropdownItem>
      <DropdownItem>
        <DropdownItemContent leadingIcon={Files}>Duplicar</DropdownItemContent>
      </DropdownItem>
      <Link
        href={`/trilhas-de-aprendizagem/${studyPlan.id}`}
        className="w-full"
      >
        <DropdownItem>
          <DropdownItemContent leadingIcon={Edit}>Editar</DropdownItemContent>
        </DropdownItem>
      </Link>
      <DropdownItem>
        <DropdownItemContent leadingIcon={Power}>Inativar</DropdownItemContent>
      </DropdownItem>
      <Separator />

      <DeleteStudyPlanAlert studyPlanMutation={handleDelete} />
    </Dropdown>
  )
}
